# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_step_by_step_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_step_by_step
        animate_step_by_step(
            scene=self,
            steps=[{'step_number': '1', 'title': '初始化数据', 'content': '## 创建数组\n```python\narr = [64, 34, 25, 12, 22, 11, 90]\n```\n- 准备待排序的数组\n- 记录数组长度\n', 'color': '#FF6B6B', 'narration': '首先我们初始化一个待排序的数组'}, {'step_number': '2', 'title': '选择最小元素', 'content': '## 查找最小值\n```python\nmin_idx = 0\nfor i in range(1, len(arr)):\n    if arr[i] < arr[min_idx]:\n        min_idx = i\n```\n- 遍历未排序部分\n- 找到最小元素的索引\n', 'color': '#4ECDC4', 'narration': '接下来在未排序部分找到最小的元素'}, {'step_number': '3', 'title': '交换元素', 'content': '## 元素交换\n```python\narr[0], arr[min_idx] = arr[min_idx], arr[0]\n```\n- 将最小元素移到已排序部分的末尾\n- 扩大已排序区域\n', 'color': '#45B7D1', 'narration': '然后将最小元素与第一个位置交换'}],
            title="选择排序算法演示",
            subtitle="逐步理解排序过程",
            intro_narration="今天我们来学习选择排序算法的工作原理",
            outro_narration="通过这三个步骤，我们完成了选择排序的一轮操作"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
