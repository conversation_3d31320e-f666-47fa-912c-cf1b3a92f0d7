# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_table_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_table
        animate_table(
            scene=self,
            headers=['城市', '4月入职', '5月离职', '5月入职', '5月离职', '6月入职', '6月离职', '主要离职原因'],
            data=[['北京', '23', '11', '15', '7', '17', '3', '家庭原因'], ['上海', '43', '2', '12', '8', '19', '11', '薪资原因'], ['深圳', '11', '3', '15', '2', '16', '5', '个人发展'], ['杭州', '15', '1', '19', '3', '15', '0', '家庭原因'], ['武汉', '17', '0', '9', '7', '7', '8', '晋升'], ['广州', '9', '1', '3', '0', '2', '2', '个人问题'], ['合计', '118', '18', '73', '27', '76', '29', '/']],
            title="城市职业变化统计表",
            highlight_rows=[1],
            narration="让我们来看看这个城市职业变化的统计表格。"
        )

        # Action 2: animate_table
        animate_table(
            scene=self,
            headers=['产品', 'Q1销量', 'Q2销量', '增长率'],
            data=[['产品A', '100', '120', '20%'], ['产品B', '80', '95', '18.8%'], ['产品C', '60', '90', '50%']],
            highlight_rows=[0, 2],
            narration="这是产品销量的对比分析表格。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
