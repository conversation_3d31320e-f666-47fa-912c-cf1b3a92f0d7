# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_side_by_side_comparison_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content="def fib_recursive(n):\n    if n <= 1:\n        return n\n    return fib_recursive(n-1) + fib_recursive(n-2)\n",
            left_type="code",
            left_title="递归斐波那契",
            right_content="def fib_iterative(n):\n    a, b = 0, 1\n    for _ in range(n):\n        a, b = b, a + b\n    return a\n",
            right_type="code",
            right_title="迭代斐波那契",
            narration="让我们比较斐波那契数列的递归和迭代实现。"
        )

        # Action 2: animate_side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content="{\n    \"name\": \"Python\",\n    \"year\": 1991,\n    \"creator\": \"Guido van Rossum\",\n    \"paradigms\": [\"面向对象\", \"命令式\", \"函数式\"]\n}\n",
            left_type="json",
            left_title="Python 信息",
            right_content="{\n    \"name\": \"Java\",\n    \"year\": 1995,\n    \"creator\": \"James Gosling\",\n    \"paradigms\": [\"面向对象\", \"命令式\"]\n}\n",
            right_type="json",
            right_title="Java 信息",
            transition="fadeIn",
            narration="Python 和 Java 的信息对比。"
        )

        # Action 3: animate_side_by_side_comparison
        animate_side_by_side_comparison(
            scene=self,
            left_content="# Python\n\n- 创建于1991年\n\n- 由Guido van Rossum开发\n\n- 支持面向对象、命令式和函数式编程\n",
            left_type="markdown",
            left_title="Python 信息",
            right_content="# Java\n\n- 创建于1995年\n\n- 由James Gosling开发\n\n- 主要支持面向对象编程\n",
            right_type="markdown",
            right_title="Java 信息",
            transition="fadeIn",
            narration="Python 和 Java 的信息对比。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
