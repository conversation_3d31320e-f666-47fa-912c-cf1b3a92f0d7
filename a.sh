echo "=== 检查每个文件的详细信息 ==="
for file in '/Users/<USER>/Documents/agentic-feynman/media/videos/storyboard_1_attempt_1_dsl/480p15/Storyboard_1_subtitle.mp4' \
'/Users/<USER>/Documents/agentic-feynman/media/videos/480p15/transition_Storyboard_1_Storyboard_2_default.mp4' \
'/Users/<USER>/Documents/agentic-feynman/media/videos/storyboard_2_attempt_1_dsl/480p15/Storyboard_2_subtitle.mp4' \
'/Users/<USER>/Documents/agentic-feynman/media/videos/480p15/transition_Storyboard_2_Storyboard_3_default.mp4' \
'/Users/<USER>/Documents/agentic-feynman/media/videos/storyboard_3_attempt_1_dsl/480p15/Storyboard_3_subtitle.mp4'; do
    echo "--- $(basename "$file") ---"
    ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate,time_base,duration -of csv=p=0 "$file"
    echo
done

