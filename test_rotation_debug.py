"""
测试旋转转场效果的调试脚本
"""

from manim import *
from dsl.v2.core.transition_effects import TransitionManager

class RotationDebugScene(Scene):
    """调试旋转转场效果"""

    def construct(self):
        # 创建两个测试对象
        old_obj = VGroup(
            Text("旧内容", font_size=48, color=BLUE),
            Rectangle(width=3, height=1, color=BLUE, fill_opacity=0.3)
        )

        new_obj = VGroup(
            Text("新内容", font_size=48, color=RED),
            Rectangle(width=3, height=1, color=RED, fill_opacity=0.3)
        )

        # 显示旧内容
        self.add(old_obj)
        self.wait(1)

        print("开始测试 rotate_out_in 转场效果...")

        # 使用 TransitionManager 来应用转场效果
        TransitionManager.apply_transition(
            scene=self,
            old_mobj=old_obj,
            new_mobj=new_obj,
            transition_type="rotate",
            run_time=2.0
        )

        self.wait(2)


class SpiralDebugScene(Scene):
    """调试螺旋转场效果"""

    def construct(self):
        # 创建两个测试对象
        old_obj = VGroup(
            Text("旧内容", font_size=48, color=GREEN),
            Circle(radius=1, color=GREEN, fill_opacity=0.3)
        )

        new_obj = VGroup(
            Text("新内容", font_size=48, color=YELLOW),
            Circle(radius=1, color=YELLOW, fill_opacity=0.3)
        )

        # 显示旧内容
        self.add(old_obj)
        self.wait(1)

        print("开始测试 spiral_out 转场效果...")

        # 使用 TransitionManager 来应用转场效果
        TransitionManager.apply_transition(
            scene=self,
            old_mobj=old_obj,
            new_mobj=new_obj,
            transition_type="spiral",
            run_time=2.0
        )

        self.wait(2)


class SimpleRotationTestScene(Scene):
    """简单的旋转测试"""

    def construct(self):
        # 创建一个简单的对象
        obj = Text("测试旋转", font_size=48, color=BLUE)
        self.add(obj)
        self.wait(1)

        # 直接测试 Rotate 动画
        print("测试直接的 Rotate 动画...")
        self.play(Rotate(obj, angle=PI, run_time=1.0))
        self.wait(1)

        # 测试 animate.rotate
        print("测试 animate.rotate...")
        self.play(obj.animate.rotate(PI), run_time=1.0)
        self.wait(1)
