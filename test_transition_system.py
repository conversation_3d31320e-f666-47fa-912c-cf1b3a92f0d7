"""
转场系统测试脚本
"""

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions.animate_markdown import animate_markdown
from dsl.v2.core.inter_scene_transition import generate_inter_scene_transition
import os
import shutil
from pathlib import Path


class TransitionTestScene(FeynmanScene):
    """测试转场效果的场景"""
    
    def construct(self):
        # 清理之前的状态文件
        states_dir = Path("temp/scene_states")
        if states_dir.exists():
            shutil.rmtree(states_dir)
        
        # 测试同一分镜内的转场效果
        self.test_intra_scene_transitions()
        
        # 测试分镜间转场的状态保存
        self.test_inter_scene_state_saving()
    
    def test_intra_scene_transitions(self):
        """测试同一分镜内的转场效果"""
        self.logger.info("开始测试同一分镜内的转场效果")
        
        # 第一个内容
        animate_markdown(
            scene=self,
            content="# 第一个内容\n\n这是第一个测试内容，用于展示转场效果。",
            narration="这是第一个测试内容",
            id="content_1"
        )
        
        self.wait(1)
        
        # 第二个内容 - 这里会触发转场效果
        animate_markdown(
            scene=self,
            content="# 第二个内容\n\n这是第二个测试内容，展示从第一个内容的转场。",
            narration="现在切换到第二个内容",
            id="content_2"
        )
        
        self.wait(1)
        
        # 第三个内容 - 再次触发转场
        animate_markdown(
            scene=self,
            content="# 第三个内容\n\n这是第三个测试内容，展示连续的转场效果。",
            narration="最后切换到第三个内容",
            id="content_3"
        )
        
        self.wait(2)
    
    def test_inter_scene_state_saving(self):
        """测试分镜间转场的状态保存"""
        self.logger.info("测试场景状态保存功能")
        
        # 创建一些内容并保存状态
        animate_markdown(
            scene=self,
            content="# 分镜A结束\n\n这是分镜A的结束内容。",
            narration="这是分镜A的结束",
            id="scene_a_end"
        )
        
        self.wait(1)
        
        # 清除并创建新内容
        animate_markdown(
            scene=self,
            content="# 分镜B开始\n\n这是分镜B的开始内容。",
            narration="这是分镜B的开始",
            id="scene_b_start"
        )
        
        self.wait(1)


def test_transition_effects():
    """测试各种转场效果"""
    print("测试转场效果...")
    
    # 渲染测试场景
    os.system("manim test_transition_system.py TransitionTestScene -ql")
    
    print("转场效果测试完成！")


def test_inter_scene_transition_generation():
    """测试分镜间转场生成"""
    print("测试分镜间转场生成...")
    
    # 首先运行测试场景以生成状态文件
    test_transition_effects()
    
    # 生成分镜间转场
    success = generate_inter_scene_transition(
        from_scene_id="scene_a_end",
        to_scene_id="scene_b_start",
        transition_type="slide_left",
        output_file="test_inter_scene_transition.py"
    )
    
    if success:
        print("分镜间转场代码生成成功！")
        print("生成的文件: test_inter_scene_transition.py")
        
        # 尝试渲染转场视频
        try:
            os.system("manim test_inter_scene_transition.py InterSceneTransitionScene -ql")
            print("转场视频渲染成功！")
        except Exception as e:
            print(f"转场视频渲染失败: {e}")
    else:
        print("分镜间转场代码生成失败！")


def show_available_transitions():
    """显示所有可用的转场效果"""
    from dsl.v2.core.transition_effects import TransitionManager
    
    print("可用的转场效果:")
    for name in TransitionManager.TRANSITION_EFFECTS.keys():
        print(f"  - {name}")


def main():
    """主测试函数"""
    print("=" * 50)
    print("转场系统测试")
    print("=" * 50)
    
    # 显示可用转场效果
    show_available_transitions()
    print()
    
    # 测试同一分镜内转场
    print("1. 测试同一分镜内转场效果...")
    test_transition_effects()
    print()
    
    # 测试分镜间转场生成
    print("2. 测试分镜间转场生成...")
    test_inter_scene_transition_generation()
    print()
    
    print("所有测试完成！")
    print("\n检查以下文件:")
    print("- media/videos/test_transition_system/480p15/TransitionTestScene.mp4")
    print("- media/videos/test_inter_scene_transition/480p15/InterSceneTransitionScene.mp4")
    print("- temp/scene_states/ (状态文件)")


if __name__ == "__main__":
    main()
