import pickle
import shutil
from pathlib import Path
from typing import Optional

from loguru import logger
from manim import *


def save_scene_state(scene_name: str, current_mobj: Mobject, save_dir: str, content_type: str, mobject_id: str):
    """
    保存场景状态到文件，用于分镜间转场

    文件名使用当前类的名称（Storyboard_xxx），同一个分镜中，每个动效函数会覆盖之前动效函数的状态，因为每个分镜之需要保存最终的状态，所以我们只需要保存最后一个动效函数的状态即可

    Args:
        scene_name: 场景名称
        current_mobj: 当前场景中的对象
        save_dir: 场景状态存储目录
        content_type: 内容类型，用于在加载时进行判断需要返回什么
        mobject_id: Mobject的唯一标识符，用于在加载时找到正确的对象
    """
    if not current_mobj:
        return

    # 确保目录存在
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)

    try:
        # 使用pickle保存完整的Mobject状态
        scene_state_file = save_dir / f"{scene_name}.pkl"
        mobj_state_file = save_dir / f"{mobject_id}.pkl"
        save_data = {
            "content_type": content_type,
            "mobj": current_mobj,
        }
        with open(scene_state_file, "wb") as f:
            pickle.dump(save_data, f)
        shutil.copy(scene_state_file, mobj_state_file)

        logger.info(f"场景状态已保存: {scene_state_file}, {mobj_state_file}")

    except Exception as e:
        logger.warning(f"保存场景状态失败: {e}")
        # 创建一个空文件，防止加载时出错或者错位
        Path(scene_state_file).touch()
        Path(mobj_state_file).touch()


def load_scene_state(save_dir: str, scene_id: str, is_old_mobject: bool = True) -> Optional[Mobject]:
    """
    从文件加载场景状态

    Args:
        save_dir: 场景状态存储目录
        scene_id: 场景唯一标识符

    Returns:
        场景状态数据，如果文件不存在返回None
    """
    try:
        # 加载pickle文件
        state_file = Path(save_dir) / f"{scene_id}.pkl"
        if state_file.exists():
            with open(state_file, "rb") as f:
                data = pickle.load(f)
            content_type = data["content_type"]
            mobj: Mobject = data["mobj"]
            logger.info(f"加载场景状态: {scene_id}, 类型: {content_type}, 原始对象: {mobj}")

            if is_old_mobject:
                return mobj

            # NOTE: 不同类型的内容返回不同的对象来完成转场效果，需要和对应函数中的动画匹配
            # 比如 markdown 返回第一个 submobject，那么在 animate_markdown 中，
            # 第一个 submobject 就不能以动画形式出现，而必须用add，才能和转场之后的
            # 状态保持一致
            target = None
            if content_type == "markdown":
                if mobj.submobjects:
                    target = mobj.submobjects[0]
                else:
                    target = mobj
            elif content_type == "timeline":
                # 如果timeline有title_group (类型为VGroup)，则返回title_group
                # 如果没有，返回None，因为时间轴中的元素最后会被移动到正中，和初始位置不同，无法转场
                if isinstance(mobj.submobjects[0], VGroup):
                    target = mobj.submobjects[0]
            elif content_type == "text_only":
                # TODO 将title拆分出来？
                target = None
            else:
                # TODO 增加其他类型的处理，目前默认不返回，避免出问题
                target = None
            logger.info(f"加载场景状态: {scene_id}, 类型: {content_type}, 对象: {target}")
            return target
    except Exception as e:
        logger.warning(f"加载场景状态失败: {e}")
        import traceback

        traceback.print_exc()
    return None
