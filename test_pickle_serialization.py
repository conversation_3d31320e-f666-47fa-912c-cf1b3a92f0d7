"""
测试pickle序列化功能
"""

from manim import *
from dsl.v2.core.scene import FeynmanScene
import shutil
from pathlib import Path
import pickle


class PickleSerializationTest(FeynmanScene):
    """测试pickle序列化的场景"""
    
    def construct(self):
        # 清理之前的状态文件
        states_dir = Path("temp/scene_states")
        if states_dir.exists():
            shutil.rmtree(states_dir)
        
        # 禁用语音功能
        self.skip_animations = False
        
        # 测试pickle序列化
        self.test_pickle_serialization()
    
    def test_pickle_serialization(self):
        """测试pickle序列化功能"""
        self.logger.info("开始测试pickle序列化")
        
        # 创建一个复杂的Mobject
        content = VGroup(
            Text("测试内容", font_size=48, color=BLUE),
            Text("这是一个复杂的对象", font_size=24, color=WHITE).shift(DOWN),
            Rectangle(width=4, height=2, stroke_color=RED, fill_opacity=0.2).shift(DOWN * 2),
            Circle(radius=0.5, color=GREEN).shift(UP * 2 + LEFT * 2),
            Arrow(start=LEFT, end=RIGHT, color=YELLOW).shift(UP * 2 + RIGHT * 2)
        )
        
        # 显示内容
        self.play(FadeIn(content))
        self.current_mobj = content
        self.wait(1)
        
        # 保存状态
        self.save_scene_state("test_content", "end")
        
        # 清除内容
        self.play(FadeOut(content))
        self.current_mobj = None
        self.wait(0.5)
        
        # 加载状态
        loaded_state = self.load_scene_state("test_content", "end")
        
        if loaded_state and 'mobj' in loaded_state:
            # 显示加载的内容
            loaded_mobj = loaded_state['mobj']
            self.play(FadeIn(loaded_mobj))
            self.current_mobj = loaded_mobj
            self.wait(2)
            
            # 测试转场
            new_content = VGroup(
                Text("加载成功！", font_size=48, color=GREEN),
                Text("pickle序列化工作正常", font_size=24, color=WHITE).shift(DOWN)
            )
            
            # 应用转场
            self.clear_current_mobj()  # 这会使用转场效果
            self.play(FadeIn(new_content))
            self.current_mobj = new_content
            self.wait(2)
        else:
            # 显示错误信息
            error_content = VGroup(
                Text("加载失败", font_size=48, color=RED),
                Text("pickle序列化有问题", font_size=24, color=WHITE).shift(DOWN)
            )
            self.play(FadeIn(error_content))
            self.wait(2)


def run_command(cmd: str):
    """运行命令，优先使用uv，否则使用虚拟环境"""
    import os
    import subprocess
    from pathlib import Path
    
    # 检查是否有uv
    try:
        subprocess.run(["uv", "--version"], capture_output=True, check=True)
        uv_available = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        uv_available = False
    
    if uv_available:
        # 使用uv运行
        full_cmd = f"uv run {cmd}"
        print(f"使用uv运行: {full_cmd}")
        os.system(full_cmd)
    else:
        # 检查虚拟环境
        venv_path = Path(".venv/bin/activate")
        if venv_path.exists():
            # 使用虚拟环境
            full_cmd = f"source .venv/bin/activate && {cmd}"
            print(f"使用虚拟环境运行: {full_cmd}")
            os.system(full_cmd)
        else:
            # 直接运行
            print(f"直接运行: {cmd}")
            os.system(cmd)


def test_pickle_functionality():
    """测试pickle功能"""
    print("测试pickle序列化功能...")
    
    # 创建测试对象
    test_obj = VGroup(
        Text("测试", color=BLUE),
        Circle(radius=1, color=RED)
    )
    
    try:
        # 测试pickle序列化
        pickled_data = pickle.dumps(test_obj)
        print(f"✅ pickle序列化成功，数据大小: {len(pickled_data)} bytes")
        
        # 测试反序列化
        unpickled_obj = pickle.loads(pickled_data)
        print(f"✅ pickle反序列化成功，对象类型: {type(unpickled_obj)}")
        
        return True
        
    except Exception as e:
        print(f"❌ pickle测试失败: {e}")
        return False


def check_state_files():
    """检查状态文件"""
    states_dir = Path("temp/scene_states")
    if states_dir.exists():
        print(f"\n📁 状态文件目录: {states_dir}")
        
        # 检查pickle文件
        pkl_files = list(states_dir.glob("*.pkl"))
        json_files = list(states_dir.glob("*.json"))
        
        print(f"  📦 Pickle文件: {len(pkl_files)} 个")
        for file in pkl_files:
            print(f"    - {file.name}")
            try:
                with open(file, 'rb') as f:
                    data = pickle.load(f)
                    print(f"      场景ID: {data.get('scene_id')}")
                    print(f"      状态类型: {data.get('state_type')}")
                    print(f"      对象类型: {data.get('mobj_type')}")
                    print(f"      有Mobject: {'mobj' in data and data['mobj'] is not None}")
            except Exception as e:
                print(f"      读取失败: {e}")
        
        print(f"  📄 JSON文件: {len(json_files)} 个")
        for file in json_files:
            print(f"    - {file.name}")
    else:
        print("❌ 状态文件目录不存在")


def main():
    """主测试函数"""
    print("=" * 60)
    print("Pickle序列化测试")
    print("=" * 60)
    
    # 测试基础pickle功能
    print("1. 测试基础pickle功能...")
    if test_pickle_functionality():
        print("   基础pickle功能正常\n")
    else:
        print("   基础pickle功能有问题，可能影响后续测试\n")
    
    # 运行场景测试
    print("2. 运行场景测试...")
    run_command("manim test_pickle_serialization.py PickleSerializationTest -ql")
    
    # 检查状态文件
    print("\n3. 检查状态文件...")
    check_state_files()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    print("\n📁 生成的文件:")
    print("  • media/videos/test_pickle_serialization/480p15/PickleSerializationTest.mp4")
    print("  • temp/scene_states/*.pkl (状态文件)")


if __name__ == "__main__":
    main()
