# 转场功能集成文档

## 概述

`process_storyboard.py` 现已集成转场功能，可以在分镜视频之间自动生成转场视频，并将其拼接到最终输出中。

## 功能特性

### 1. 自动转场视频生成
- 在每两个分镜视频之间自动生成转场视频
- 支持多种转场效果：淡入淡出、左滑、缩放等
- 转场视频的清晰度与分镜视频保持一致（由 `--quality` 参数控制）

### 2. 智能视频序列构建
- 自动构建包含转场的完整视频序列：分镜1 → 转场1 → 分镜2 → 转场2 → ... → 分镜N
- 转场生成失败时自动跳过，不影响主要分镜的拼接
- 支持转场功能的开启/关闭

### 3. 质量一致性
- 转场视频使用与分镜视频相同的质量设置
- 支持所有 Manim 质量级别：`l`(480p), `m`(720p), `h`(1080p), `k`(2160p)

## 使用方法

### 基本用法

```bash
# 使用默认设置处理分镜（包含转场）
python process_storyboard.py storyboard.json

# 指定质量级别
python process_storyboard.py storyboard.json --quality h

# 指定输出目录
python process_storyboard.py storyboard.json --output-dir output/videos
```

### 配置选项

转场功能通过 `StoryboardProcessor` 类的以下属性控制：

```python
# 在代码中配置转场
processor = StoryboardProcessor(...)
processor.transition_enabled = True      # 启用/禁用转场功能
processor.transition_run_time = 1.0      # 转场动画时长（秒）
```

### 支持的转场类型

1. **fade_out_in**（默认）- 淡入淡出转场
2. **slide_left** - 左滑转场
3. **zoom** - 缩放转场

## 技术实现

### 核心方法

1. **`_generate_transition_video()`** - 生成单个转场视频
2. **`_create_video_sequence_with_transitions()`** - 创建包含转场的完整视频序列
3. **`_concatenate_videos()`** - 拼接视频（已更新支持转场）
4. **`_save_scene_state_for_transition()`** - 保存场景状态用于转场
5. **`_find_transition_video_file()`** - 查找渲染后的转场视频文件

### 转场视频生成流程

```
1. 为每对相邻分镜生成转场ID（scene_i_end -> scene_j_start）
2. 保存分镜场景状态到pickle文件
3. 调用generate_inter_scene_transition()直接渲染转场视频
4. 如果真实转场失败，回退到简化转场效果
5. 将转场视频插入到视频序列中
6. 使用ffmpeg拼接完整序列
```

### 转场系统架构

系统采用两层转场机制：

1. **真实转场**：使用`generate_inter_scene_transition()`基于实际场景状态生成转场
2. **简化转场**：当真实转场失败时，使用简单的几何图形创建转场效果

### 文件结构

```
output/
├── generated_manim_code/
│   ├── storyboard_1_dsl.py          # 分镜代码
│   └── transition_*.py              # 转场代码
├── scene_states/                    # 场景状态（预留）
└── final_video.mp4                  # 最终输出
```

## 配置示例

### 完整处理流程

```bash
# 完整流程：DSL生成 → 代码生成 → 渲染 → 字幕 → 拼接（含转场）
python process_storyboard.py storyboard.json \
    --quality h \
    --stages "dsl,code,render,subtitles,concat" \
    --project-name "my_project"
```

### 仅测试转场功能

```bash
# 只生成DSL，不渲染视频（用于测试）
python process_storyboard.py storyboard.json \
    --stages "dsl" \
    --quality l
```

## 故障排除

### 常见问题

1. **转场视频生成失败**
   - 检查 Manim 是否正确安装
   - 确认输出目录有写权限
   - 查看日志中的详细错误信息

2. **视频拼接失败**
   - 确认 ffmpeg 已安装并在 PATH 中
   - 检查视频文件是否存在且格式正确
   - 查看临时文件列表是否正确生成

3. **转场效果不理想**
   - 调整 `transition_run_time` 参数
   - 尝试不同的转场类型
   - 检查视频质量设置是否一致

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 性能考虑

- 转场视频生成会增加总处理时间
- 每个转场视频通常为1-2秒，文件较小
- 可以通过禁用转场功能来加快处理速度
- 建议在最终输出前先用低质量测试

## 扩展性

### 添加新的转场类型

在 `_generate_simple_transition_code()` 方法中添加新的转场效果：

```python
elif "{transition_type}" == "my_transition":
    # 自定义转场代码
    pass
```

### 自定义转场参数

可以扩展配置系统来支持更多转场参数，如颜色、速度等。
