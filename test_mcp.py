import asyncio
from camel.toolkits import MCPToolkit

async def main():
    mcp_config_path = "config/mcp_servers_config.json"
    mcp_toolkit = MCPToolkit(config_path=mcp_config_path)
    
    print("Attempting to connect asynchronously...")
    try:
        await mcp_toolkit.connect()
        print("Connected successfully!")
        
        tools = mcp_toolkit.get_tools()
        print("Available tools:", tools)
        
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        # Check if 'is_connected' is the correct attribute or method
        # This is a common pattern, but the exact name might vary.
        # If MCPToolkit doesn't have such a property, we might need to adjust.
        if hasattr(mcp_toolkit, 'is_connected') and mcp_toolkit.is_connected:
            print("Attempting to disconnect...")
            await mcp_toolkit.disconnect()
            print("Disconnected.")
        elif hasattr(mcp_toolkit, '_connected') and mcp_toolkit._connected: # Common private attribute
            print("Attempting to disconnect (using _connected)...")
            await mcp_toolkit.disconnect()
            print("Disconnected (using _connected).")
        else:
            # If we can't check connection status, try disconnecting anyway if connect was attempted
            # This assumes disconnect() is safe to call even if not fully connected or already disconnected.
            # A more robust solution would be to know the exact state management of MCPToolkit.
            print("Attempting to disconnect (status unknown)...")
            try:
                await mcp_toolkit.disconnect() # Ensure server process is cleaned up
                print("Disconnect call completed (status unknown).")
            except Exception as e:
                print(f"Error during disconnect (status unknown): {e}")

if __name__ == "__main__":
    asyncio.run(main())
